# Digger Card - 卡片创建应用

一个基于 Next.js 15 + React 19 的现代化卡片创建和管理应用。

## 功能特性

### 🎨 卡片创建功能
- **智能表单设计**: 用户友好的卡片创建表单，支持标题、描述、分类等字段
- **实时预览**: 边编辑边预览，所见即所得的卡片设计体验
- **表单验证**: 完整的输入验证和错误提示系统
- **响应式设计**: 完美适配桌面端和移动端设备

### 🛠️ 技术栈
- **Next.js 15**: 使用最新的 App Router 和服务端组件
- **React 19**: 利用最新的 React 特性和性能优化
- **TypeScript**: 完整的类型安全保障
- **Tailwind CSS 4**: 现代化的样式系统
- **本地存储**: 支持卡片数据的本地保存和管理

## 页面结构

- `/` - 主页面，展示应用介绍和导航
- `/create-card` - 卡片创建页面，包含表单和预览功能

## 使用方法

### 创建新卡片
1. 访问 `/create-card` 页面
2. 填写卡片信息：
   - **标题**: 卡片的主要标题（必填）
   - **描述**: 卡片的详细描述（可选）
   - **分类**: 选择卡片分类（必填）
   - **标签**: 添加相关标签（可选）
3. 实时查看右侧预览效果
4. 点击"保存卡片"完成创建

### 表单验证规则
- 标题：2-50个字符，不能为空
- 描述：最多200个字符
- 分类：必须选择一个有效分类
- 标签：每个标签最多20个字符

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
