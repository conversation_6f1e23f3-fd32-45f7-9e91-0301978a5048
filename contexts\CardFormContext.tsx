'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { 
  CreateCardFormData, 
  FormErrors, 
  CardPriority, 
  DEFAULT_CARD_CONFIG 
} from '@/types/card';

/**
 * 卡片表单上下文接口
 */
interface CardFormContextType {
  // 表单数据
  formData: CreateCardFormData;
  // 表单错误
  errors: FormErrors;
  // 提交状态
  isSubmitting: boolean;
  // 成功状态
  showSuccess: boolean;
  
  // 更新表单字段
  updateField: (field: keyof CreateCardFormData, value: string) => void;
  // 设置错误信息
  setFieldError: (field: keyof FormErrors, error: string | undefined) => void;
  // 验证表单
  validateForm: () => boolean;
  // 重置表单
  resetForm: () => void;
  // 提交表单
  submitForm: () => Promise<void>;
  // 设置提交状态
  setSubmitting: (submitting: boolean) => void;
  // 设置成功状态
  setSuccess: (success: boolean) => void;
}

/**
 * 默认表单数据
 */
const defaultFormData: CreateCardFormData = {
  title: '',
  description: '',
  category: '',
  priority: CardPriority.MEDIUM,
  tags: '',
  dueDate: '',
  color: DEFAULT_CARD_CONFIG.color
};

// 创建上下文
const CardFormContext = createContext<CardFormContextType | undefined>(undefined);

/**
 * 卡片表单上下文提供者组件
 */
export function CardFormProvider({ children }: { children: ReactNode }) {
  // 状态管理
  const [formData, setFormData] = useState<CreateCardFormData>(defaultFormData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  /**
   * 更新表单字段
   */
  const updateField = useCallback((field: keyof CreateCardFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除对应字段的错误信息
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  }, [errors]);

  /**
   * 设置字段错误信息
   */
  const setFieldError = useCallback((field: keyof FormErrors, error: string | undefined) => {
    setErrors(prev => ({
      ...prev,
      [field]: error
    }));
  }, []);

  /**
   * 验证表单数据
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};

    // 验证标题
    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    } else if (formData.title.trim().length < 2) {
      newErrors.title = '标题至少需要2个字符';
    } else if (formData.title.trim().length > DEFAULT_CARD_CONFIG.maxTitleLength) {
      newErrors.title = `标题不能超过${DEFAULT_CARD_CONFIG.maxTitleLength}个字符`;
    }

    // 验证描述
    if (formData.description.length > DEFAULT_CARD_CONFIG.maxDescriptionLength) {
      newErrors.description = `描述不能超过${DEFAULT_CARD_CONFIG.maxDescriptionLength}个字符`;
    }

    // 验证分类
    if (!formData.category) {
      newErrors.category = '请选择卡片分类';
    }

    // 验证标签
    if (formData.tags.trim()) {
      const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      if (tags.length > DEFAULT_CARD_CONFIG.maxTagsCount) {
        newErrors.tags = `标签数量不能超过${DEFAULT_CARD_CONFIG.maxTagsCount}个`;
      } else {
        const invalidTag = tags.find(tag => tag.length > DEFAULT_CARD_CONFIG.maxTagLength);
        if (invalidTag) {
          newErrors.tags = `标签"${invalidTag}"超过${DEFAULT_CARD_CONFIG.maxTagLength}个字符限制`;
        }
      }
    }

    // 验证截止日期
    if (formData.dueDate) {
      const dueDate = new Date(formData.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (dueDate < today) {
        newErrors.dueDate = '截止日期不能早于今天';
      }
    }

    // 验证颜色格式
    if (formData.color && !/^#[0-9A-Fa-f]{6}$/.test(formData.color)) {
      newErrors.title = '请输入有效的颜色代码（如：#3B82F6）';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  /**
   * 重置表单
   */
  const resetForm = useCallback(() => {
    setFormData(defaultFormData);
    setErrors({});
    setShowSuccess(false);
  }, []);

  /**
   * 提交表单
   */
  const submitForm = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 创建卡片数据
      const cardData = {
        id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category as any,
        priority: formData.priority,
        tags: formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0),
        createdAt: new Date(),
        updatedAt: new Date(),
        dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
        color: formData.color || DEFAULT_CARD_CONFIG.color,
        isCompleted: false
      };

      // 保存到本地存储
      const existingCards = JSON.parse(localStorage.getItem('digger-cards') || '[]');
      existingCards.push(cardData);
      localStorage.setItem('digger-cards', JSON.stringify(existingCards));
      
      console.log('卡片保存成功:', cardData);
      
      // 显示成功提示
      setShowSuccess(true);
      
      // 重置表单
      setTimeout(() => {
        resetForm();
      }, 1500);

      // 3秒后隐藏成功提示
      setTimeout(() => setShowSuccess(false), 3000);
      
    } catch (error) {
      console.error('保存卡片失败:', error);
      setFieldError('title', '保存失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, resetForm, setFieldError]);

  /**
   * 设置提交状态
   */
  const setSubmitting = useCallback((submitting: boolean) => {
    setIsSubmitting(submitting);
  }, []);

  /**
   * 设置成功状态
   */
  const setSuccess = useCallback((success: boolean) => {
    setShowSuccess(success);
  }, []);

  // 上下文值
  const contextValue: CardFormContextType = {
    formData,
    errors,
    isSubmitting,
    showSuccess,
    updateField,
    setFieldError,
    validateForm,
    resetForm,
    submitForm,
    setSubmitting,
    setSuccess
  };

  return (
    <CardFormContext.Provider value={contextValue}>
      {children}
    </CardFormContext.Provider>
  );
}

/**
 * 使用卡片表单上下文的Hook
 */
export function useCardForm() {
  const context = useContext(CardFormContext);
  if (context === undefined) {
    throw new Error('useCardForm must be used within a CardFormProvider');
  }
  return context;
}

/**
 * 表单验证工具函数
 */
export const formValidators = {
  /**
   * 验证标题
   */
  validateTitle: (title: string): string | undefined => {
    if (!title.trim()) {
      return '标题不能为空';
    }
    if (title.trim().length < 2) {
      return '标题至少需要2个字符';
    }
    if (title.trim().length > DEFAULT_CARD_CONFIG.maxTitleLength) {
      return `标题不能超过${DEFAULT_CARD_CONFIG.maxTitleLength}个字符`;
    }
    return undefined;
  },

  /**
   * 验证描述
   */
  validateDescription: (description: string): string | undefined => {
    if (description.length > DEFAULT_CARD_CONFIG.maxDescriptionLength) {
      return `描述不能超过${DEFAULT_CARD_CONFIG.maxDescriptionLength}个字符`;
    }
    return undefined;
  },

  /**
   * 验证分类
   */
  validateCategory: (category: string): string | undefined => {
    if (!category) {
      return '请选择卡片分类';
    }
    return undefined;
  },

  /**
   * 验证标签
   */
  validateTags: (tags: string): string | undefined => {
    if (!tags.trim()) {
      return undefined;
    }

    const tagArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    
    if (tagArray.length > DEFAULT_CARD_CONFIG.maxTagsCount) {
      return `标签数量不能超过${DEFAULT_CARD_CONFIG.maxTagsCount}个`;
    }

    const invalidTag = tagArray.find(tag => tag.length > DEFAULT_CARD_CONFIG.maxTagLength);
    if (invalidTag) {
      return `标签"${invalidTag}"超过${DEFAULT_CARD_CONFIG.maxTagLength}个字符限制`;
    }

    return undefined;
  },

  /**
   * 验证截止日期
   */
  validateDueDate: (dueDate: string): string | undefined => {
    if (!dueDate) {
      return undefined;
    }

    const date = new Date(dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (date < today) {
      return '截止日期不能早于今天';
    }

    return undefined;
  },

  /**
   * 验证颜色
   */
  validateColor: (color: string): string | undefined => {
    if (!color) {
      return undefined;
    }

    if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
      return '请输入有效的颜色代码（如：#3B82F6）';
    }

    return undefined;
  }
};
