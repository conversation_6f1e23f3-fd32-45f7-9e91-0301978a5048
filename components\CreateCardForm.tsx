"use client";

import {
  CardCategory,
  CardPriority,
  CardCategoryLabels,
  CardPriorityLabels,
  DEFAULT_CARD_CONFIG,
} from "@/types/card";
import { useCardForm } from "@/contexts/CardFormContext";

/**
 * 卡片创建表单组件
 * 提供完整的卡片信息输入界面和实时验证
 */
export default function CreateCardForm() {
  // 使用表单上下文
  const {
    formData,
    errors,
    isSubmitting,
    showSuccess,
    updateField,
    submitForm,
    resetForm,
  } = useCardForm();

  /**
   * 处理输入字段变化
   * @param field 字段名
   * @param value 字段值
   */
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    updateField(field, value);
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 这里应该调用实际的保存API
      console.log("保存卡片数据:", formData);

      // 显示成功提示
      setShowSuccess(true);

      // 重置表单
      setFormData({
        title: "",
        description: "",
        category: "",
        priority: CardPriority.MEDIUM,
        tags: "",
        dueDate: "",
        color: DEFAULT_CARD_CONFIG.color,
      });

      // 3秒后隐藏成功提示
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error("保存卡片失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * 重置表单
   */
  const handleReset = () => {
    setFormData({
      title: "",
      description: "",
      category: "",
      priority: CardPriority.MEDIUM,
      tags: "",
      dueDate: "",
      color: DEFAULT_CARD_CONFIG.color,
    });
    setErrors({});
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 成功提示 */}
      {showSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-green-500 mr-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p className="text-sm font-medium text-green-800 dark:text-green-200">
              卡片创建成功！
            </p>
          </div>
        </div>
      )}

      {/* 标题输入 */}
      <div>
        <label
          htmlFor="title"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          卡片标题 <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange("title", e.target.value)}
          placeholder="请输入卡片标题（2-50个字符）"
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
            errors.title
              ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
              : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
          } text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400`}
          maxLength={DEFAULT_CARD_CONFIG.maxTitleLength}
        />
        {errors.title && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.title}
          </p>
        )}
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {formData.title.length}/{DEFAULT_CARD_CONFIG.maxTitleLength}
        </p>
      </div>

      {/* 描述输入 */}
      <div>
        <label
          htmlFor="description"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          卡片描述
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder="请输入卡片描述（可选，最多200个字符）"
          rows={4}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 resize-none ${
            errors.description
              ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
              : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
          } text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400`}
          maxLength={DEFAULT_CARD_CONFIG.maxDescriptionLength}
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {errors.description}
          </p>
        )}
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {formData.description.length}/
          {DEFAULT_CARD_CONFIG.maxDescriptionLength}
        </p>
      </div>

      {/* 分类和优先级选择 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* 分类选择 */}
        <div>
          <label
            htmlFor="category"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            卡片分类 <span className="text-red-500">*</span>
          </label>
          <select
            id="category"
            value={formData.category}
            onChange={(e) => handleInputChange("category", e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
              errors.category
                ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
                : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
            } text-gray-900 dark:text-white`}
          >
            <option value="">请选择分类</option>
            {Object.entries(CardCategoryLabels).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.category}
            </p>
          )}
        </div>

        {/* 优先级选择 */}
        <div>
          <label
            htmlFor="priority"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            优先级
          </label>
          <select
            id="priority"
            value={formData.priority}
            onChange={(e) => handleInputChange("priority", e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            {Object.entries(CardPriorityLabels).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 标签和截止日期 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* 标签输入 */}
        <div>
          <label
            htmlFor="tags"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            标签
          </label>
          <input
            type="text"
            id="tags"
            value={formData.tags}
            onChange={(e) => handleInputChange("tags", e.target.value)}
            placeholder="用逗号分隔多个标签"
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
              errors.tags
                ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
                : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
            } text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400`}
          />
          {errors.tags && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.tags}
            </p>
          )}
        </div>

        {/* 截止日期 */}
        <div>
          <label
            htmlFor="dueDate"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            截止日期
          </label>
          <input
            type="date"
            id="dueDate"
            value={formData.dueDate}
            onChange={(e) => handleInputChange("dueDate", e.target.value)}
            min={new Date().toISOString().split("T")[0]}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
              errors.dueDate
                ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
                : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
            } text-gray-900 dark:text-white`}
          />
          {errors.dueDate && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.dueDate}
            </p>
          )}
        </div>
      </div>

      {/* 颜色选择 */}
      <div>
        <label
          htmlFor="color"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          卡片颜色
        </label>
        <div className="flex items-center space-x-4">
          <input
            type="color"
            id="color"
            value={formData.color}
            onChange={(e) => handleInputChange("color", e.target.value)}
            className="w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"
          />
          <input
            type="text"
            value={formData.color}
            onChange={(e) => handleInputChange("color", e.target.value)}
            placeholder="#3B82F6"
            className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <button
          type="submit"
          disabled={isSubmitting}
          className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
        >
          {isSubmitting ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              保存中...
            </>
          ) : (
            "保存卡片"
          )}
        </button>

        <button
          type="button"
          onClick={handleReset}
          disabled={isSubmitting}
          className="flex-1 sm:flex-none bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-medium py-3 px-6 rounded-lg transition-colors duration-200"
        >
          重置表单
        </button>
      </div>
    </form>
  );
}
