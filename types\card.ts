// 卡片相关的TypeScript类型定义

/**
 * 卡片分类枚举
 * 定义卡片可选的分类类型
 */
export enum CardCategory {
  WORK = 'work',           // 工作相关
  PERSONAL = 'personal',   // 个人生活
  STUDY = 'study',         // 学习笔记
  PROJECT = 'project',     // 项目管理
  IDEA = 'idea',           // 创意想法
  TODO = 'todo',           // 待办事项
  NOTE = 'note',           // 普通笔记
  OTHER = 'other'          // 其他类型
}

/**
 * 卡片分类显示标签映射
 * 用于在UI中显示中文标签
 */
export const CardCategoryLabels: Record<CardCategory, string> = {
  [CardCategory.WORK]: '工作',
  [CardCategory.PERSONAL]: '个人',
  [CardCategory.STUDY]: '学习',
  [CardCategory.PROJECT]: '项目',
  [CardCategory.IDEA]: '创意',
  [CardCategory.TODO]: '待办',
  [CardCategory.NOTE]: '笔记',
  [CardCategory.OTHER]: '其他'
};

/**
 * 卡片优先级枚举
 */
export enum CardPriority {
  LOW = 'low',       // 低优先级
  MEDIUM = 'medium', // 中等优先级
  HIGH = 'high',     // 高优先级
  URGENT = 'urgent'  // 紧急
}

/**
 * 卡片优先级显示标签映射
 */
export const CardPriorityLabels: Record<CardPriority, string> = {
  [CardPriority.LOW]: '低',
  [CardPriority.MEDIUM]: '中',
  [CardPriority.HIGH]: '高',
  [CardPriority.URGENT]: '紧急'
};

/**
 * 卡片数据接口
 * 定义单个卡片的完整数据结构
 */
export interface Card {
  id: string;                    // 卡片唯一标识符
  title: string;                 // 卡片标题（必填，2-50字符）
  description?: string;          // 卡片描述（可选，最多200字符）
  category: CardCategory;        // 卡片分类（必填）
  priority?: CardPriority;       // 卡片优先级（可选，默认为medium）
  tags: string[];               // 卡片标签数组（每个标签最多20字符）
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 最后更新时间
  isCompleted?: boolean;        // 是否已完成（用于待办类型卡片）
  dueDate?: Date;               // 截止日期（可选）
  color?: string;               // 卡片颜色（可选，用于个性化显示）
}

/**
 * 创建卡片表单数据接口
 * 用于表单输入和验证
 */
export interface CreateCardFormData {
  title: string;                 // 卡片标题
  description: string;           // 卡片描述
  category: CardCategory | '';   // 卡片分类（表单中可能为空字符串）
  priority: CardPriority;        // 卡片优先级
  tags: string;                  // 标签字符串（逗号分隔）
  dueDate: string;               // 截止日期字符串
  color: string;                 // 卡片颜色
}

/**
 * 表单验证错误接口
 * 定义各字段的错误信息
 */
export interface FormErrors {
  title?: string;        // 标题字段错误信息
  description?: string;  // 描述字段错误信息
  category?: string;     // 分类字段错误信息
  tags?: string;         // 标签字段错误信息
  dueDate?: string;      // 截止日期字段错误信息
}

/**
 * 卡片列表查询参数接口
 * 用于筛选和排序卡片
 */
export interface CardQueryParams {
  category?: CardCategory;       // 按分类筛选
  priority?: CardPriority;       // 按优先级筛选
  isCompleted?: boolean;         // 按完成状态筛选
  searchTerm?: string;           // 搜索关键词
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'priority'; // 排序字段
  sortOrder?: 'asc' | 'desc';    // 排序方向
  limit?: number;                // 返回数量限制
  offset?: number;               // 分页偏移量
}

/**
 * 卡片统计信息接口
 * 用于显示卡片数量统计
 */
export interface CardStats {
  total: number;                 // 总卡片数
  byCategory: Record<CardCategory, number>; // 按分类统计
  byPriority: Record<CardPriority, number>; // 按优先级统计
  completed: number;             // 已完成数量
  pending: number;               // 待处理数量
  overdue: number;               // 过期数量
}

/**
 * 本地存储键名常量
 * 用于localStorage的键名管理
 */
export const STORAGE_KEYS = {
  CARDS: 'digger-cards',         // 卡片数据存储键
  SETTINGS: 'digger-settings',   // 应用设置存储键
  LAST_CATEGORY: 'digger-last-category' // 最后选择的分类
} as const;

/**
 * 默认卡片配置
 */
export const DEFAULT_CARD_CONFIG = {
  priority: CardPriority.MEDIUM,  // 默认优先级
  color: '#3B82F6',              // 默认颜色（蓝色）
  maxTitleLength: 50,            // 标题最大长度
  maxDescriptionLength: 200,     // 描述最大长度
  maxTagLength: 20,              // 单个标签最大长度
  maxTagsCount: 10               // 最大标签数量
} as const;
