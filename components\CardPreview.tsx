'use client';

import { useState, useEffect } from 'react';
import { 
  CardCategory, 
  CardPriority, 
  CardCategoryLabels, 
  CardPriorityLabels,
  CreateCardFormData,
  DEFAULT_CARD_CONFIG
} from '@/types/card';

/**
 * 卡片预览组件
 * 实时显示卡片的最终效果
 */
export default function CardPreview() {
  // 预览数据状态（从表单同步）
  const [previewData, setPreviewData] = useState<CreateCardFormData>({
    title: '',
    description: '',
    category: '',
    priority: CardPriority.MEDIUM,
    tags: '',
    dueDate: '',
    color: DEFAULT_CARD_CONFIG.color
  });

  // 监听表单数据变化（这里简化处理，实际项目中可能需要使用Context或状态管理库）
  useEffect(() => {
    // 模拟从表单获取数据的逻辑
    // 在实际项目中，这里应该通过Context或props获取表单数据
    const handleFormDataChange = (event: CustomEvent) => {
      setPreviewData(event.detail);
    };

    window.addEventListener('formDataChange', handleFormDataChange as EventListener);
    
    return () => {
      window.removeEventListener('formDataChange', handleFormDataChange as EventListener);
    };
  }, []);

  /**
   * 获取优先级颜色样式
   */
  const getPriorityColor = (priority: CardPriority): string => {
    switch (priority) {
      case CardPriority.LOW:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case CardPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case CardPriority.HIGH:
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case CardPriority.URGENT:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  /**
   * 获取分类图标
   */
  const getCategoryIcon = (category: CardCategory | string): JSX.Element => {
    switch (category) {
      case CardCategory.WORK:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
          </svg>
        );
      case CardCategory.PERSONAL:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case CardCategory.STUDY:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case CardCategory.PROJECT:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case CardCategory.IDEA:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        );
      case CardCategory.TODO:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        );
      case CardCategory.NOTE:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        );
    }
  };

  /**
   * 解析标签字符串为数组
   */
  const parseTags = (tagsString: string): string[] => {
    return tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  };

  /**
   * 格式化日期显示
   */
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const tags = parseTags(previewData.tags);
  const hasContent = previewData.title || previewData.description || previewData.category;

  return (
    <div className="space-y-4">
      {/* 预览卡片 */}
      <div 
        className="relative bg-white dark:bg-gray-800 rounded-xl shadow-md border-l-4 p-6 transition-all duration-300 hover:shadow-lg"
        style={{ 
          borderLeftColor: previewData.color || DEFAULT_CARD_CONFIG.color,
          backgroundColor: hasContent ? undefined : '#f9fafb'
        }}
      >
        {!hasContent ? (
          // 空状态提示
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 10h6m-6 4h6" />
            </svg>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              开始填写表单，这里将显示卡片预览
            </p>
          </div>
        ) : (
          <>
            {/* 卡片头部 */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                {/* 标题 */}
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {previewData.title || '卡片标题'}
                </h3>
                
                {/* 分类和优先级 */}
                <div className="flex items-center space-x-2 mb-3">
                  {previewData.category && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      {getCategoryIcon(previewData.category as CardCategory)}
                      <span className="ml-1">
                        {CardCategoryLabels[previewData.category as CardCategory] || '未知分类'}
                      </span>
                    </span>
                  )}
                  
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(previewData.priority)}`}>
                    {CardPriorityLabels[previewData.priority]}
                  </span>
                </div>
              </div>

              {/* 截止日期 */}
              {previewData.dueDate && (
                <div className="text-right">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">截止日期</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatDate(previewData.dueDate)}
                  </div>
                </div>
              )}
            </div>

            {/* 描述 */}
            {previewData.description && (
              <div className="mb-4">
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  {previewData.description}
                </p>
              </div>
            )}

            {/* 标签 */}
            {tags.length > 0 && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* 卡片底部信息 */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                刚刚创建
              </div>
              
              {/* 颜色指示器 */}
              <div className="flex items-center space-x-2">
                <div 
                  className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                  style={{ backgroundColor: previewData.color || DEFAULT_CARD_CONFIG.color }}
                  title="卡片颜色"
                />
              </div>
            </div>
          </>
        )}
      </div>

      {/* 预览说明 */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          预览说明
        </h4>
        <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
          <li>• 左侧边框颜色对应选择的卡片颜色</li>
          <li>• 分类图标会根据选择的分类自动显示</li>
          <li>• 优先级标签颜色会根据优先级自动调整</li>
          <li>• 标签会自动解析逗号分隔的文本</li>
          <li>• 截止日期会格式化为易读的日期格式</li>
        </ul>
      </div>

      {/* 响应式预览提示 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
              响应式设计
            </h4>
            <p className="text-xs text-blue-700 dark:text-blue-300">
              此卡片在不同设备上都会保持良好的显示效果，包括手机、平板和桌面设备。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
